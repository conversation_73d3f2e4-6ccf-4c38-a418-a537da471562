# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError

# Product Brand
class ProductBrand(models.Model):
    _name = 'product.brand'
    _description = 'Product Brand'
    _order = 'name asc'


    name = fields.Char(
        string='Brand', 
        required=True 
    )

    active = fields.Boolean(
        string='Active', 
        default=True
    )


    @api.constrains('name')
    def _check_unique_name(self):
        for record in self:
            if record.name:
                # Search for brands with the same name (case insensitive)
                domain = [
                    ('name', '=ilike', record.name),
                    ('id', '!=', record.id)
                ]

                if self.search_count(domain) > 0:
                    raise ValidationError(_(
                        'The Brand name "%s" already exists. '
                        'Please enter a unique brand name.') % record.name
                    )


    def unlink(self):
        """Override unlink to prevent deletion of brands in use with custom message"""
        for brand in self:
            # Check if any products are using this brand
            products = self.env['product.template'].search([('brand_id', '=', brand.id)])

            if products:
                # Get product names for the error message (limit to first 5)
                product_names = products[:5].mapped('name')
                product_list = '\n'.join(product_names)
                
                # Add ellipsis if there are more than 5 products
                if len(products) > 5:
                    product_list += f'\n... and {len(products) - 5} more'
                
                raise ValidationError(_(
                    'Cannot delete brand "%s" as it is used by the following products: \n\n%s\n\n'
                    'Please remove the brand from these products first.'
                ) % (brand.name, product_list))
                
        return super(ProductBrand, self).unlink()
