<?xml version="1.0" encoding="utf-8"?>

<odoo>
    <!-- Add brand_id to the my customized form view -->
    <record id="product_template_form_view_brand_inherit" model="ir.ui.view">
        <field name="name">product.template.form.brand.inherit</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="ar_restricted_fields.product_field_ext_product_form_view_inherit"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='brand_name']" position="after">
                <field name="brand_id" options="{'no_create': True, 'no_create_edit': True, 'no_open': True}"/>
            </xpath>
        </field>
    </record>

    <!-- Add brand_id to the studio customized tree view -->
    <record id="product_template_tree_view_brand_inherit" model="ir.ui.view">
        <field name="name">product.template.tree.brand.inherit</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="studio_customization.odoo_studio_product__7a5e77fc-bb52-461e-923f-24f4dda2850e"/>
        <field name="arch" type="xml">
            <xpath expr="//tree/field[@name='brand_name']" position="after">
                <field name="brand_id" optional="show"/>
            </xpath>
        </field>
    </record>

    <!-- Add brand_id to the search view -->
    <record id="product_template_search_view_brand_inherit" model="ir.ui.view">
        <field name="name">product.template.search.brand.inherit</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_search_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='categ_id']" position="after">
                <field name="brand_id"/>
            </xpath>
            <xpath expr="//group/filter[@name='categ_id']" position="after">
                <filter string="Brand (New)" name="brand_id" context="{'group_by':'brand_id'}"/>
            </xpath>
        </field>
    </record>
</odoo>