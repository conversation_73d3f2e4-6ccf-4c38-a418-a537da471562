<?xml version="1.0" encoding="utf-8"?>

<odoo>
    <!-- Product Brand - Form -->
    <record id="ar_product_brand_form_view" model="ir.ui.view">
        <field name="name">ar.product_brand.form.view</field>
        <field name="model">product.brand</field>
        <field name="arch" type="xml">
            <form string="Product Brand">
                <sheet>
                    <group>
                        <group col="4" colspan="4">
                            <field name="name"/>
                            <field name="active" widget="boolean_toggle"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Product Brand - Tree -->
    <record id="ar_product_brand_tree_view" model="ir.ui.view">
        <field name="name">ar.product.brand.tree.view</field>
        <field name="model">product.brand</field>
        <field name="arch" type="xml">
            <tree string="Product Brand">
                <field name="name"/>
                <field name="active" widget="boolean_toggle"/>
            </tree>
        </field>
    </record>

    <!-- Product Brand - Action -->
    <record id="ar_product_brand_action" model="ir.actions.act_window">
        <field name="name">Product Brand</field>
        <field name="res_model">product.brand</field>
        <field name="view_mode">tree,form</field>
        <field name="view_id" ref="ar_product_brand_tree_view"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a product brand
            </p>
            <p>
                You can define several product brands (e.g. Captain, Commander etc).
            </p>
        </field>
    </record>

    <!-- Product Brand - Menu -->
    <menuitem 
        id="ar_product_brand_menu" 
        name="Product Brand" 
        parent="stock.menu_product_in_config_stock" 
        action="ar_product_brand_action" 
        sequence="2"
        groups="base.group_system"
    />
</odoo>